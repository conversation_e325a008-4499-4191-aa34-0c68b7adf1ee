import { addDays } from 'date-fns';
import { ClassController } from '../controllers/classController.js';
import {
    tempScheduleSchema,
    getAllSchedulesSchema,
    getClassesSchema,
    createClassSchema,
    checkTeacherFreeSchema,
    addStudentsSchema,
    updateClassSchema
} from '../schemas/classSchemas.js';

export default function (fastify, opts) {
    const classController = new ClassController(fastify);

    // 快捷创建临时计划
    fastify.post('/classes/temp-schedule', {
        schema: tempScheduleSchema,
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('class:create')],
        handler: (request, reply) => classController.createTempSchedule(request, reply)
    });

    // 获取计划表
    fastify.get('/classes/all/schedules', {
        schema: getAllSchedulesSchema,
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('class:read')],
        handler: (request, reply) => classController.getAllSchedules(request, reply)
    });

    // 获取班级列表
    fastify.get('/classes', {
        schema: getClassesSchema,
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('class:read')],
        handler: (request, reply) => classController.getClasses(request, reply)
    });

    // 创建班级
    
    fastify.post('/classes', {
        schema: createClassSchema,
        onRequest: [
            fastify.auth.authenticate, 
            fastify.auth.requirePermission('class:create')
        ],
        handler: (request, reply) => classController.createClass(request, reply)
    });


    // 追加或复制班级计划
    fastify.post('/classes/append-or-copy-schedule/:classesId', {
        schema: {
            tags: ['classes'],
            summary: '追加或复制班级计划',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                required: ['type'],
                properties: {
                    type: { type: 'string', enum: ['append', 'copy'] },
                    appendCount: { type: 'number', default: 1 }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: async (request, reply) => {
            const { classesId } = request.params;
            const { type, appendCount } = request.body;
            const { institutionId, id: operatorId } = request.user;
            
            try {
                if (type === 'append') {
                    const classes = await fastify.prisma.classes.findFirst({
                        where: {
                            id: classesId,
                            institutionId
                        }
                    });
                    if (!classes) {
                        return reply.status(404).send({ message: '班级不存在' });
                    }

                    const classesTime = await fastify.prisma.classesTime.findFirst({
                        where: {
                            classesId,
                            institutionId
                        }
                    });
                    
                    if (!classesTime) {
                        return reply.status(404).send({ message: '班级时间不存在' });
                    }
                    
                    const { weekDay, startTime, endTime } = classesTime;
                    
                    const { recurrenceType, endDate, endType, teacherId, name,
                        courseId, maxStudentCount, isAutoCheckIn, isOnLeave,
                        isQRCodeAttendance, isShowWeekCount, leaveDeadline,
                        appointmentEndTime, appointmentStartTime, isReserve
                    } = classes;
                    
                    // 获取班级学生
                    const classStudents = await fastify.prisma.studentClasses.findMany({
                        where: {
                            classesId,
                            institutionId
                        },
                        select: {
                            studentId: true
                        }
                    });
                    
                    const studentIds = classStudents.map(s => s.studentId);
                    
                    if (recurrenceType === 'weekly') {
                        // 每周几开课
                        const newSchedules = [];
                        const lastSchedule = await fastify.prisma.classesSchedule.findFirst({
                            where: { classesId, institutionId },
                            orderBy: { startDate: 'desc' }
                        });
                        
                        const lastDate = lastSchedule ? Number(lastSchedule.startDate) : Number(endDate);
                        const newTotalWeeks = lastSchedule ? lastSchedule.totalWeeks + appendCount : appendCount;
                        
                        // 更新所有现有课节的totalWeeks
                        if (lastSchedule) {
                            await fastify.prisma.classesSchedule.updateMany({
                                where: { 
                                    classesId,
                                    institutionId 
                                },
                                data: {
                                    totalWeeks: newTotalWeeks
                                }
                            });
                        }
                        
                        for (let i = 1; i <= appendCount; i++) {
                            const newDate = addDays(new Date(lastDate), i * 7).getTime();
                            
                            const newSchedule = await fastify.prisma.classesSchedule.create({
                                data: {
                                    classesId,
                                    startDate: newDate,
                                    weekDay,
                                    startTime,
                                    endTime,
                                    currentWeeks: lastSchedule ? lastSchedule.currentWeeks + i : i,
                                    totalWeeks: newTotalWeeks,
                                    name,
                                    courseId,
                                    subject: name,
                                    teacherId,
                                    maxStudentCount,
                                    isAutoCheckIn,
                                    isOnLeave,
                                    isQRCodeAttendance,
                                    isShowWeekCount,
                                    leaveDeadline,
                                    appointmentEndTime,
                                    appointmentStartTime,
                                    isReserve,
                                    institutionId
                                }
                            });
                            
                            newSchedules.push(newSchedule);
                            
                            // 为班级学生创建课节记录
                            if (studentIds.length > 0) {
                                const scheduleStudentData = studentIds.map(studentId => ({
                                    id: fastify.uuidv4 ? fastify.uuidv4() : crypto.randomUUID(),
                                    classesScheduleId: newSchedule.id,
                                    studentId,
                                    studentType: 'fixed',
                                    operatorId,
                                    operatorTime: new Date().getTime(),
                                    institutionId
                                }));
                                
                                await fastify.prisma.studentWeeklySchedule.createMany({
                                    data: scheduleStudentData
                                });
                            }
                        }
                        
                        // 更新班级结束日期和课时数
                        await fastify.prisma.classes.update({
                            where: { id: classesId },
                            data: {
                                endDate: newSchedules[newSchedules.length - 1].startDate,
                                times: newTotalWeeks
                            }
                        });
                        
                        reply.success({
                            message: '追加班级计划成功',
                            // data: newSchedules
                        });
                    } else {
                        reply.status(400).send({ message: '仅支持每周循环的班级追加计划' });
                    }
                } else if (type === 'copy') {
                    const classes = await fastify.prisma.classes.findFirst({
                        where: {
                            id: classesId,
                            institutionId
                        },
                        include: {
                            timeDetails: true
                        }
                    });
                    
                    if (!classes) {
                        return reply.status(404).send({ message: '班级不存在' });
                    }
                    
                    // 创建新班级
                    const newClassId = fastify.uuidv4 ? fastify.uuidv4() : crypto.randomUUID();
                    const { name, teacherId, courseId, classRoomId, maxStudentCount, 
                           recurrenceType, endType, startDate, times, endDate,
                           isAutoCheckIn, isOnLeave, isQRCodeAttendance, isShowWeekCount,
                           leaveDeadline, appointmentEndTime, appointmentStartTime, isReserve } = classes;
                    
                    await fastify.prisma.classes.create({
                        data: {
                            id: newClassId,
                            name: `${name} (复制)`,
                            teacherId,
                            courseId,
                            classRoomId,
                            maxStudentCount,
                            startDate,
                            endType,
                            times,
                            endDate,
                            recurrenceType,
                            isAutoCheckIn,
                            isOnLeave,
                            isQRCodeAttendance,
                            isShowWeekCount,
                            leaveDeadline,
                            appointmentEndTime,
                            appointmentStartTime,
                            isReserve,
                            institutionId
                        }
                    });
                    
                    // 复制班级时间
                    const classesTimePromises = classes.timeDetails.map(item => {
                        const id = fastify.uuidv4 ? fastify.uuidv4() : crypto.randomUUID();
                        return fastify.prisma.classesTime.create({
                            data: {
                                id,
                                classesId: newClassId,
                                weekDay: item.weekDay,
                                startTime: item.startTime,
                                endTime: item.endTime,
                                recurrenceType: item.recurrenceType,
                                endType: item.endType,
                                startDate: item.startDate,
                                endDate: item.endDate,
                                institutionId
                            }
                        });
                    });
                    await Promise.all(classesTimePromises);
                    
                    // 复制班级计划
                    const schedules = await fastify.prisma.classesSchedule.findMany({
                        where: {
                            classesId,
                            institutionId
                        }
                    });
                    
                    const newSchedules = [];
                    for (const schedule of schedules) {
                        const newSchedule = await fastify.prisma.classesSchedule.create({
                            data: {
                                classesId: newClassId,
                                startDate: schedule.startDate,
                                weekDay: schedule.weekDay,
                                startTime: schedule.startTime,
                                endTime: schedule.endTime,
                                currentWeeks: schedule.currentWeeks,
                                totalWeeks: schedule.totalWeeks,
                                name: schedule.name,
                                courseId: schedule.courseId,
                                subject: schedule.subject,
                                teacherId: schedule.teacherId,
                                maxStudentCount: schedule.maxStudentCount,
                                isAutoCheckIn: schedule.isAutoCheckIn,
                                isOnLeave: schedule.isOnLeave,
                                isQRCodeAttendance: schedule.isQRCodeAttendance,
                                isShowWeekCount: schedule.isShowWeekCount,
                                leaveDeadline: schedule.leaveDeadline,
                                appointmentEndTime: schedule.appointmentEndTime,
                                appointmentStartTime: schedule.appointmentStartTime,
                                isReserve: schedule.isReserve,
                                institutionId
                            }
                        });
                        newSchedules.push(newSchedule);
                    }
                    
                    reply.success({
                        message: '复制班级计划成功',
                        data: { classId: newClassId }
                    });
                }
            } catch (error) {
                fastify.log.error(error);
                reply.status(500).send({ message: error.message || '操作班级计划失败' });
            }
        }
    });

    // 获取课程老师空闲时间
    fastify.post('/classes/check-teacher-free', {
        schema: checkTeacherFreeSchema,
        onRequest: [fastify.auth.authenticate, fastify.auth.requirePermission('class:read')],
        handler: (request, reply) => classController.checkTeacherFree(request, reply)
    });

    // 获取班级id和name
    fastify.get('/classes/select', {
        schema: {
            tags: ['classes'],
            summary: '获取班级id和name',
            querystring: {
                type: 'object',
                properties: {
                    page: { type: 'number', default: 1 },
                    pageSize: { type: 'number', default: 10 },
                    name: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.getClassesSelect(request, reply)
    });

    // 班级添加课程
    fastify.post('/classes/:classesId/courses', {
        schema: {
            tags: ['classes'],
            summary: '班级添加课程',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                required: ['courseId'],
                properties: {
                    courseId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.addClassCourse(request, reply)
    });

    // 班级更新课程
    fastify.put('/classes/:classesId/courses', {
        schema: {
            tags: ['classes'],
            body: {
                type: 'object',
                required: ['courseId'],
                properties: {
                    courseId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.updateClassCourse(request, reply)
    });

    // 班级添加学员
    fastify.post('/classes/:classesId/students', {
        schema: addStudentsSchema,
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.addStudents(request, reply)
    });

    // 班级删除学员
    fastify.delete('/classes/:classesId/students/:studentId', {
        schema: {
            tags: ['classes'],
            summary: '班级删除学员',
            params: {
                type: 'object',
                required: ['classesId', 'studentId'],
                properties: {
                    classesId: { type: 'string' },
                    studentId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.removeStudent(request, reply)
    });

    // 更新班级
    fastify.put('/classes/:classesId', {
        schema: updateClassSchema,
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.updateClass(request, reply)
    });

    // 获取班级详情
    fastify.get('/classes/:classesId', {
        schema: {
            tags: ['classes'],
            summary: '获取班级详情',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.getClassDetail(request, reply)
    });

    // 获取班级课程章节列表
    fastify.get('/classes/:classesId/schedules', {
        schema: {
            tags: ['classes'],
            summary: '获取班级课程章节列表',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.getClassSchedules(request, reply)
    });

    // 获取班级章节详情
    fastify.get('/classes/schedules/:scheduleId', {
        schema: {
            tags: ['classes'],
            summary: '获取班级章节详情',
            params: {
                type: 'object',
                required: ['scheduleId'],
                properties: {
                    scheduleId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.getScheduleDetail(request, reply)
    });

    // 更新班级课节内容
    fastify.put('/classes/schedules/:scheduleId', {
        schema: {
            tags: ['classes'],
            summary: '更新班级课节内容',
            params: {
                type: 'object',
                required: ['scheduleId'],
                properties: {
                    scheduleId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.updateSchedule(request, reply)
    });

    // 班级章节添加学生
    fastify.post('/classes/schedules/:scheduleId/students', {
        schema: {
            tags: ['classes'],
            summary: '班级章节添加学生',
            params: {
                type: 'object',
                required: ['scheduleId'],
                properties: {
                    scheduleId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                // required: ['students'],
                properties: {
                    students: {
                        type: 'object',
                        properties: {
                            studentId: { type: 'string' },
                            type: {
                                type: 'string',
                                default: 'temporary',
                                enum: ['fixed', 'temporary', 'trial']
                            }
                        }
                    },
                    studentIds: {
                        type: 'array',
                        items: { 
                            type: 'string'
                        },
                        default: []
                    },
                    type: {
                        type: 'string',
                        default: 'temporary',
                        enum: ['fixed', 'temporary', 'trial']
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.addScheduleStudent(request, reply)
    });

    // 班级章节删除学生
    fastify.delete('/classes/schedules/:scheduleId/students', {
        schema: {
            tags: ['classes'],
            summary: '班级章节删除学生',
            params: {
                type: 'object',
                required: ['scheduleId'],
                properties: {
                    scheduleId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                required: ['studentIds'],
                properties: {
                    studentIds: {
                        type: 'array',
                        items: { type: 'string' },
                        default: []
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.removeScheduleStudents(request, reply)
    });

    // 班级计划学员考勤
    fastify.put('/classes/schedules/:scheduleId/studentAttendance', {
        schema: {
            tags: ['classes'],
            summary: '班级计划学员考勤',
            params: {
                type: 'object',
                required: ['scheduleId'],
                properties: {
                    scheduleId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                required: ['studentId', 'status'],
                properties: {
                    studentId: { type: 'string' },
                    status: {
                        type: 'string',
                        default: 'unattended',
                        enum: ['unattended', 'attendance', 'leave', 'absent']
                    }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.updateStudentAttendance(request, reply)
    });

    // 获取班级学生
    fastify.get('/classes/:classesId/students', {
        schema: {
            tags: ['classes'],
            summary: '获取班级学生',
            params: {
                type: 'object',
                required: ['classesId'],
                properties: {
                    classesId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.getClassStudents(request, reply)
    });

    // 获取班级章节学生出勤记录
    fastify.get('/classes/:classesId/schedules/:scheduleId/studentAttendance', {
        schema: {
            tags: ['classes'],
            summary: '获取班级章节学生出勤记录',
            params: {
                type: 'object',
                properties: {
                    classesId: { type: 'string' },
                    scheduleId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.getScheduleAttendance(request, reply)
    });

    // 获取班级考勤记录
    fastify.get('/classes/:classesId/attendance', {
        schema: {
            tags: ['classes'],
            summary: '获取班级考勤记录',
            params: {
                type: 'object',
                properties: {
                    classesId: { type: 'string' }
                }
            }
        },
        onRequest: [fastify.auth.authenticate],
        handler: (request, reply) => classController.getClassAttendance(request, reply)
    });
} 
