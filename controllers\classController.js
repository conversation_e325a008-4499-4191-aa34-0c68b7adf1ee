import { ClassService } from '../services/classService.js';
import { createError } from '@fastify/error';

const AUTH_ERROR = createError('AUTH_ERROR', '%s', 401);
const INTERNAL_ERROR = createError('INTERNAL_ERROR', '%s', 500);

export class ClassController {
    constructor(fastify) {
        this.classService = new ClassService(fastify.prisma, fastify.redis, fastify.pg);
        this.fastify = fastify;
    }

    async createTempSchedule(request, reply) {
        try {
            await this.classService.createTempSchedule(request.body, request.user.institutionId);
            reply.success({
                message: '快捷创建临时计划成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '快捷创建临时计划失败');
        }
    }

    async getAllSchedules(request, reply) {
        try {
            const schedules = await this.classService.getAllSchedules(request.query, request.user.institutionId);
            reply.success({
                data: schedules,
                message: '获取计划表成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '获取计划表失败');
        }
    }

    async getClasses(request, reply) {
        try {
            const result = await this.classService.getClasses(request.query, request.user.institutionId);
            reply.success({
                data: result,
                message: '获取班级列表成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '获取班级列表失败');
        }
    }

    async createClass(request, reply) {
        try {
            const classId = await this.classService.createClass(
                request.body,
                request.user.institutionId,
                request.user.id
            );
            reply.success({
                message: '创建班级成功！'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '创建班级失败');
        }
    }

    async checkTeacherFree(request, reply) {
        try {
            const teachers = await this.classService.checkTeacherFree(
                request.body,
                request.user.institutionId
            );
            reply.success({
                data: teachers,
                message: '获取老师空闲时间成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '获取老师空闲时间失败');
        }
    }

    async getClassesSelect(request, reply) {
        try {
            const result = await this.classService.getClassesSelect(request.query, request.user.institutionId);
            reply.success({
                data: result,
                message: '获取班级选择列表成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '获取班级选择列表失败');
        }
    }

    async addClassCourse(request, reply) {
        try {
            await this.classService.addClassCourse(
                request.params.classesId,
                request.body.courseId,
                request.user.institutionId
            );
            reply.success({
                message: '班级添加课程成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '班级添加课程失败');
        }
    }

    async updateClassCourse(request, reply) {
        try {
            await this.classService.updateClassCourse(
                request.params.classesId,
                request.body.courseId,
                request.user.institutionId
            );
            reply.success({
                message: '班级更新课程成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '班级更新课程失败');
        }
    }

    async addStudents(request, reply) {
        try {
            await this.classService.addStudents(
                request.params.classesId,
                request.body.studentIds,
                request.user.institutionId,
                request.user.id
            );
            reply.success({
                message: '班级添加学员成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '班级添加学员失败');
        }
    }

    async removeStudent(request, reply) {
        try {
            await this.classService.removeStudent(
                request.params.classesId,
                request.params.studentId,
                request.user.institutionId
            );
            reply.success({
                message: '班级删除学员成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '班级删除学员失败');
        }
    }

    async updateClass(request, reply) {
        try {
            await this.classService.updateClass(
                request.params.classesId,
                request.body,
                request.user.institutionId
            );
            reply.success({
                message: '更新班级成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '更新班级失败');
        }
    }

    async getClassDetail(request, reply) {
        try {
            const result = await this.classService.getClassDetail(
                request.params.classesId,
                request.user.institutionId
            );
            reply.success({
                data: result,
                message: '获取班级详情成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '获取班级详情失败');
        }
    }

    async getClassSchedules(request, reply) {
        try {
            const result = await this.classService.getClassSchedules(
                request.params.classesId,
                request.user.institutionId
            );
            reply.success({
                data: result,
                message: '获取班级课程章节列表成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '获取班级课程章节列表失败');
        }
    }

    async getScheduleDetail(request, reply) {
        try {
            const result = await this.classService.getScheduleDetail(
                request.params.scheduleId,
                request.user.institutionId
            );
            reply.success({
                data: result,
                message: '获取班级章节详情成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '获取班级章节详情失败');
        }
    }

    async updateSchedule(request, reply) {
        try {
            await this.classService.updateSchedule(
                request.params.scheduleId,
                request.body,
                request.user.institutionId
            );
            reply.success({
                message: '更新班级课节内容成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '更新班级课节内容失败');
        }
    }

    async addScheduleStudent(request, reply) {
        try {

            const { students, type, studentIds } = request.body;
            const body = { students, type, studentIds }
            await this.classService.addScheduleStudent(
                request.params.scheduleId,
                body,
                request.user.institutionId,
                request.user.id
            );
            reply.success({
                message: '班级章节添加学生成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '班级章节添加学生失败');
        }
    }

    async removeScheduleStudents(request, reply) {
        try {
            await this.classService.removeScheduleStudents(
                request.params.scheduleId,
                request.body.studentIds,
                request.user.institutionId
            );
            reply.success({
                message: '班级章节删除学生成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '班级章节删除学生失败');
        }
    }

    async updateStudentAttendance(request, reply) {
        try {
            await this.classService.updateStudentAttendance(
                request.params.scheduleId,
                request.body.studentId,
                request.body.status,
                request.user.institutionId,
                request.user.id
            );
            reply.success({
                message: '班级计划学员考勤成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '班级计划学员考勤失败');
        }
    }

    async getClassStudents(request, reply) {
        try {
            const result = await this.classService.getClassStudents(
                request.params.classesId,
                request.user.institutionId
            );
            reply.success({
                data: result,
                message: '获取班级学生成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '获取班级学生失败');
        }
    }

    async getScheduleAttendance(request, reply) {
        try {
            const result = await this.classService.getScheduleAttendance(
                request.params.classesId,
                request.params.scheduleId,
                request.user.institutionId
            );
            reply.success({
                data: result,
                message: '获取班级章节学生出勤记录成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '获取班级章节学生出勤记录失败');
        }
    }

    async getClassAttendance(request, reply) {
        try {
            const result = await this.classService.getClassAttendance(
                request.params.classesId,
                request.user.institutionId
            );
            reply.success({
                data: result,
                message: '获取班级考勤记录成功'
            });
        } catch (error) {
            this.fastify.log.error(error);
            throw new INTERNAL_ERROR(error.message || '获取班级考勤记录失败');
        }
    }
} 