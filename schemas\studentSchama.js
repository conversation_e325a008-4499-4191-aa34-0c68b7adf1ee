export const studentSchema = {
	tags: ['students'],
	summary: '获取学生列表',
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'string', default: 1 },
			pageSize: { type: 'string', default: 10 },
			search: { type: 'string' },
			follower: { type: 'string' },
			intention: { type: 'string' },
			intentLevel: { type: 'string' },
			
			type: {
				type: 'string',
				enum: ['formal', 'intent', 'public', 'graduated', 'all'],
				default: 'formal'
			},
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'object',
					properties: {
						total: { type: 'number' },
						page: { type: 'number' },
						pageSize: { type: 'number' },
						list: {
							type: 'array',
							items: {
								type: 'object',
								properties: {
									id: { type: 'string' },
									name: { type: 'string' },
									phone: { type: 'number' },
									gender: { type: 'string' },
									address: { type: 'string' },
									remarks: { type: 'string' },
									type: { type: 'string' },
									intentLevel: { type: 'string' },
									followUpDate: { type: 'number' },
									birthday: { type: 'number' },
									status: { type: 'string' },
									source: { type: 'string' },
									sourceDesc: { type: 'string' },
									createdAt: { type: 'number' },
									followerId: { type: 'string' },
									followerName: { type: 'string' },
								}
							}
						}
					}
				}
			}
		}
	}
}
// 获取单个学生信息
export const studentByIdSchema = {
	tags: ['students'],
	summary: '获取单个学生信息',
	params: {
		type: 'object',
		properties: {
			studentId: { type: 'string' }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'object',
					properties: {
						id: { type: 'string' },
						name: { type: 'string' },
						phone: { type: 'string' },
						gender: { type: 'string' },
						address: { type: 'string' },
						remarks: { type: 'string' },
						type: { type: 'string' },
						intentLevel: { type: 'string' },
						followUpDate: { type: 'string' },
						birthday: { type: 'number' },
						status: { type: 'string' },
						source: { type: 'string' },
						sourceDesc: { type: 'string' },
						createdAt: { type: 'string' },
						followerId: { type: 'string' },
						followerName: { type: 'string' }
					}
				}
			}
		}
	}
}
// 更新学生信息
export const updateStudentSchema = {
	tags: ['students'],
	summary: '更新学生信息',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: {
				type: 'string'
			}
		}
	},
	body: {
		type: 'object',
		properties: {
			name: { type: 'string' },
			gender: { type: 'string' },
			age: { type: 'string' },
			birthday: { type: 'string' },
			phone: { type: 'string' },
			email: { type: 'string' },
			balance: { type: 'string' },
			points: { type: 'string' },
			followUpPerson: { type: 'string' },
			followUpDate: { type: 'string' },
			source: { type: 'string' },
			referrer: { type: 'string' },
			address: { type: 'string' },
			idCard: { type: 'string' },
			school: { type: 'string' },
			intentionLevel: { type: 'string' },
			parentName: { type: 'string' },
			status: { type: 'string' },
			type: { type: 'string' },
			remark: { type: 'string' },
		},
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: { type: 'object' }
			}
		}
	}
}
// 批量删除学员
export const deleteStudentSchema = {
	tags: ['students'],
	summary: '批量删除学员',
	body: {
		type: 'object',
		properties: {
			studentIds: { type: 'array', items: { type: 'string' } }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: { type: 'object' }
			}
		}
	}
}
// 获取学生上课记录
export const classesHistorySchema = {
	tags: ['students'],
	summary: '获取学生上课记录',
	params: {
		type: 'object',
		properties: {
			studentId: { type: 'string' }
		}
	},
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'string', default: 1 },
			pageSize: { type: 'string', default: 10 },
			startDate: { type: 'string' },
			endDate: { type: 'string' }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'object',
					properties: {
						list: {
							type: 'array', items: {
								type: 'object',
								properties: {
									id: { type: 'string' },
									status: { type: 'string' },
									scheduleId: { type: 'string' },
									subject: { type: 'string' },
									startDate: { type: 'number' },
									startTime: { type: 'string' },
									endTime: { type: 'string' },
									className: { type: 'string' },
									courseName: { type: 'string' },
									teacherName: { type: 'string' }
								}
							}
						},
						total: { type: 'number' },
						page: { type: 'number' },
						pageSize: { type: 'number' }
					}
				}
			}
		}
	}
}
// 获取学生购买套餐
export const productsSchema = {
	tags: ['students'],
	summary: '获取学生购买套餐',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	querystring: {
		type: 'object',
		properties: {
			status: {
				type: 'string',
				default: ''
			}
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'array',
					items: {
						type: 'object',
						properties: {
							id: { type: 'string' },
							startDate: { type: 'number' },
							endDate: { type: 'number' },
							totalSessionCount: { type: 'number' },
							remainingSessionCount: { type: 'number' },
							enrollmentStatus: { type: 'string' },
							productId: { type: 'string' },
							productName: { type: 'string' },
							productPackageType: { type: 'string' },
						}
					}
				}
			}
		}
	}
}
// 获取学生购买记录
export const productsRecordsSchema = {
	tags: ['students'],
	summary: '获取学生购买记录',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'array',
					items: {
						type: 'object',
						properties: {
							id: { type: 'string' },
							amount: { type: 'number' },
							amountPaid: { type: 'number' },
							amountUnpaid: { type: 'number' },
							paymentTime: { type: 'number' },
							paymentMethod: { type: 'string' },
							discount: { type: 'number' },
							giftCount: { type: 'number' },
							giftDays: { type: 'number' },
							purchaseQuantity: { type: 'number' },
							status: { type: 'string' },
							createdAt: { type: 'string' },
							operatorName: { type: 'string' },
							salesRepName: { type: 'string' },
							studentProductId: { type: 'string' },
							studentProductStartDate: { type: 'number' },
							studentProductEndDate: { type: 'number' },
							studentProductTotalSessionCount: { type: 'number' },
							studentProductRemainingSessionCount: { type: 'number' },
							studentProductEnrollmentStatus: { type: 'string' },
							studentProductPaymentStatus: { type: 'string' },
							studentProductPaymentTime: { type: 'string' },
							productId: { type: 'string' },
							productName: { type: 'string' },
							productPackageType: { type: 'string' }
						}
					}
				}
			}
		}
	}
}
// 获取学生考勤记录
export const attendanceSchema = {
	tags: ['students'],
	summary: '获取学生考勤记录',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'number', default: 1 },
			pageSize: { type: 'number', default: 10 },
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'object',
					properties: {
						list: {
							type: 'array',
							items: {
								type: 'object',
								properties: {
									id: { type: 'string' },
									status: { type: 'string' },
									attendanceCount: { type: 'number' },
									operatorTime: { type: 'number' },
									operatorName: { type: 'string' },
									studentId: { type: 'string' },
									studentName: { type: 'string' },
									productName: { type: 'string' },
									scheduleId: { type: 'string' },
									startDate: { type: 'number' },
									startTime: { type: 'string' },
									endTime: { type: 'string' },
									subject: { type: 'string' },
									courseName: { type: 'string' },
									classesName: { type: 'string' }
								}
							}
						},
						total: { type: 'number' },
						page: { type: 'number' },
						pageSize: { type: 'number' }
					}
				}
			}
		}
	}
}
// 获取学员班级
export const classesSchema = {
	tags: ['students'],
	summary: '获取学员班级',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'array',
					items: {
						type: 'object',
						properties: {
							id: { type: 'string' },
							joinDate: { type: 'number' },
							operatorTime: { type: 'number' },
							type: { type: 'string' },
							classesId: { type: 'string' },
							classesName: { type: 'string' },
							courseName: { type: 'string' },
							teacherName: { type: 'string' }
						}
					}
				}
			}
		}
	}
}

// 学员退出班级
export const outClassesSchema = {
	tags: ['students'],
	summary: '学员退出班级',
	params: {
		type: 'object',
		required: ['classesId', 'studentId'],
		properties: {
			classesId: { type: 'string' },
			studentId: { type: 'string' }
		}
	},

}

// 获取学员跟进记录
export const getFollowRecordsSchema = {
	tags: ['students'],
	summary: '获取跟进记录',
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'number', default: 1 },
			pageSize: { type: 'number', default: 10 },
			teacherId: { type: 'string' },
			search: { type: 'string' },
			endTime: { type: 'number' },
			startTime: { type: 'number' }
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'array',
					items: {
						type: 'object',
						properties: {
							followUpContent: { type: 'string' },
							id: { type: 'string' },
							nextFollowUpDate: { type: 'number' },
							followUpDate: { type: 'number' },
							followUpUserName: { type: 'string' },
							nextFollowUpDate: { type: 'number'}
						}
					}
				}
			}
		}
	}
}
// 新增学员跟进记录
export const addFollowRecordsSchema = {
	tags: ['students'],
	summary: '新增学员跟进记录',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		properties: {
			nextFollowUpDate: { type: 'number' },
			followUpDate: { type: 'number' },
			followUpContent: { type: 'string' },
			followUpUserId: { type: 'string' },
			intentLevel: { type: 'string' },
		}
	}
}
// 创建学生产品
export const createStudentProductSchema = {
	tags: ['students'],
	summary: '创建学生产品',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		properties: {
			productId: { type: 'string' },
			amount: { type: 'number' },
			prepaidAmount: { type: 'number' }, // 支付金额
			balance: { type: 'number' }, // 为0时，表示全额支付
			bonusLessons: { type: 'number' }, // 赠送课时
			dateTime: { type: 'number' }, // 支付时间
			remarks: { type: 'string', default: '' },
			payment: { type: 'string' }, // 支付方式
			salesRep: { type: 'string' }, // 销售代表
		}
	}
}
// 更新学生产品
export const updateStudentProductSchema = {
	tags: ['students'],
	summary: '更新学生产品',
	params: {
		type: 'object',
		required: ['studentId', 'studentProductId'],
		properties: {
			studentId: { type: 'string' },
			studentProductId: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		properties: {
			status: { type: 'string' },
			totalCount: { type: 'string' },
		}
	}
}
// 创建学员跟进人
export const createFollowUpSchema = {
	tags: ['students'],
	summary: '创建学员跟进人',
	params: {
		type: 'object',
		required: ['studentId'],
		properties: {
			studentId: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		properties: {
			followUpDate: { type: 'number' },
			followUpContent: { type: 'string' },
		}
	}
}
// 获取简易学员列表
export const simpleStudentListSchema = {
	tags: ['students'],
	summary: '获取简易学员列表',
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'number', default: 1 },
			pageSize: { type: 'number', default: 10 },
			search: { type: 'string' },
		}
	}
}

export const getStudentProductsSchema = {
	tags: ['students'],
	summary: '获取学员套餐列表',
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'number', default: 1 },
			pageSize: { type: 'number', default: 10 },
			search: { type: 'string' },
			remainingTimesMin: { type: 'number' },
			remainingTimesMax: { type: 'number' },
			remainingDaysMin: { type: 'number' },
			remainingDaysMax: { type: 'number' },
			status: { type: 'string' },
		}
	}
}

export const getStudentProductAdjustmentsSchema = {
	tags: ['students'],
	summary: '获取学员产品调整记录',
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'number', default: 1 },
			pageSize: { type: 'number', default: 10 },
			search: { type: 'string' },
			teacherId: { type: 'string' },
			endTime: { type: 'number' },
			startTime: { type: 'number' },
		}
	},
	response: {
		200: {
			type: 'object',
			properties: {
				code: { type: 'number' },
				message: { type: 'string' },
				data: {
					type: 'object',
					properties: {
						list: {
							type: 'array',
							items: {
								type: 'object',
								properties: {
									id: { type: 'string' },
									beforeCount: { type: 'number' },
									afterCount: { type: 'number' },
									beforeDays: { type: 'number' },
									afterDays: { type: 'number' },
									type: { type: 'string' },
									operatorName: { type: 'string' },
									operatorTime: { type: 'number' },
									remarks: { type: 'string' },
									productId: { type: 'string' },
									productName: { type: 'string' },
									productPackageType: { type: 'string' },
									studentId: { type: 'string' },
									studentName: { type: 'string' },
									studentPhone: { type: 'string' },
								}
							}
						},
						total: { type: 'number' },
						page: { type: 'number' },
						pageSize: { type: 'number' }
					}
				}
			}
		}
	}
}

export const classesQuerySchema = {
	tags: ['students'],
	summary: '获取学员上课记录',
	querystring: {
		type: 'object',
		properties: {
			page: { type: 'number' },
			pageSize: { type: 'number' },
			search: { type: 'string' },
			studentId: { type: 'string' },
			status: {
				type: 'string',
				enum: ['all', 'unattended', 'attendance', 'leave', 'absent'],
				default: 'all'
			},
			startDate: { type: 'string' },
			endDate: { type: 'string' },
		}
	}
}
// 获取意向学员列表的schema
export const getIntentStudentsSchema = {
    tags: ['students'],
    summary: '获取意向学员列表',
    querystring: {
        type: 'object',
        properties: {
            search: { type: 'string' },
            page: { type: 'number', default: 1 },
            pageSize: { type: 'number', default: 10 }
        }
    }
}

// 获取考勤记录的schema
export const getAttendanceRecordsSchema = {
    tags: ['students'],
    summary: '获取学员考勤记录',
    querystring: {
        type: 'object',
        properties: {
            search: { type: 'string' },
            page: { type: 'number', default: 1 },
            pageSize: { type: 'number', default: 10 },
            status: {
                type: 'string',
                default: 'attendance',
                enum: ['attendance', 'leave', 'absent', 'unattended']
            },
            endTime: { type: 'number' },
            startTime: { type: 'number' },
            teacherId: { type: 'string' }
        }
    }
} 
// 学员产品退款的schema
export const refundProductSchema = {
    tags: ['students'],
    summary: '学员产品退款',
    body: {
        type: 'object',
        properties: {
            refundReason: { type: 'string', default: '' },
            paymentMethod: { type: 'string', default: 'cash' },
        }
    }
}

// 获取整体学员上课查询的schema
export const getClassesQuerySchema = {
    tags: ['students'],
    summary: '获取学员上课记录',
    querystring: {
        type: 'object',
        properties: {
            page: { type: 'number' },
            pageSize: { type: 'number' },
            search: { type: 'string' },
            studentId: { type: 'string' },
            status: {
                type: 'string',
                enum: ['all', 'unattended', 'attendance', 'leave', 'absent'],
                default: 'all'
            },
            startDate: { type: 'string' },
            endDate: { type: 'string' },
        }
    }
}

// 获取学员套餐列表的schema
export const studentProductsListSchema = {
    tags: ['students'],
    summary: '获取学员套餐列表',
    querystring: {
        type: 'object',
        properties: {
            page: { type: 'number' },
            pageSize: { type: 'number' },
            search: { type: 'string' },
            remainingTimesMin: { type: 'number' },
            remainingTimesMax: { type: 'number' },
            remainingDaysMin: { type: 'number' },
            remainingDaysMax: { type: 'number' },
            status: { type: 'string' },
        }
    }

} // 机构添加学员的schema
export const addStudentSchema = {
    tags: ['students'],
    summary: '添加学生',
    body: {
        type: 'object',
        required: ['name', 'phone'],
        properties: {
            name: { type: 'string' },
            gender: { type: 'string' },
            phone: { type: 'string' },
            birthday: { type: 'string' },
            source: { type: 'string' },
            sourceDesc: { type: 'string' },
            referrer: { type: 'string' },
            follower: { type: 'string' },
            idCard: { type: 'string' },
            address: { type: 'string' },
            remarks: { type: 'string' },
            intention: { type: 'string' },
            type: { 
                type: 'string', 
                enum: ['formal', 'intent', 'public', 'graduated'], 
                default: 'formal' 
            },
        }
    }
};

// 学员创建跟进人的schema
export const createFollowUpPersonSchema = {
    tags: ['students'],
    summary: '学员创建跟进人',
    body: {
        type: 'object',
        required: ['studentId'],
        properties: {
            studentId: { type: 'string' }
        }
    }
};

// 注册学生的schema
export const registerStudentSchema = {
    tags: ['students'],
    summary: '注册学生',
    params: {
        type: 'object',
        required: ['institutionId'],
        properties: {
            institutionId: {
                type: 'string'
            }
        }
    },
    body: {
        type: 'object',
        required: ['name', 'phone', 'gender'],
        properties: {
            name: { type: 'string' },
            gender: { type: 'string' },
            phone: { type: 'string' }
        }
    }
}; 

// 调整学生套餐内容的schema
export const adjustStudentProductSchema = {
    tags: ['students'],
    summary: '更新学生套餐内容',
    params: {
        type: 'object',
        required: ['studentProductId'],
        properties: {
            studentProductId: { type: 'string' }
        }
    },
    body: {
        type: 'object',
        properties: {
            remainingCount: { type: 'number', default: 0 },
            remarks: { type: 'string', default: '' },
            remainingDays: { type: 'number', default: 0 },
            status: { type: 'string', default: 'active' }
        }
    }
}; 




export default {
	studentSchema,
	studentByIdSchema,
	updateStudentSchema,
	deleteStudentSchema,
	classesHistorySchema,
	productsSchema,
	productsRecordsSchema,
	attendanceSchema,
	classesSchema,
	outClassesSchema,
	getFollowRecordsSchema,
	addFollowRecordsSchema,
	createStudentProductSchema,
	updateStudentProductSchema,
	getStudentProductsSchema,
	getStudentProductAdjustmentsSchema,
	classesQuerySchema,
	getIntentStudentsSchema,
	getAttendanceRecordsSchema,
	refundProductSchema,
	getClassesQuerySchema,
	studentProductsListSchema,
	addStudentSchema,
	createFollowUpPersonSchema,
	registerStudentSchema,
	adjustStudentProductSchema
}

