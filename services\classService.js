import { v4 as uuidv4 } from 'uuid';
import { TwitterSnowflake } from "@sapphire/snowflake";
import { getDaysInRange, getTuesdaysInWeeks, getWeekDayDatesInRange } from "../utils/getWeekdayDate.js";
import getWeekStartAndEnd from '../utils/getWeekStartAndEnd.js';
import { sendToSpecifiedUser } from '../libs/websocket.js';

// 确保时间字符串格式化为两位数小时
function formatTimeString(timeStr) {
    const [hours, minutes] = timeStr.split(':');
    return `${hours.padStart(2, '0')}:${minutes}`;
}

export class ClassService {
    constructor(prisma, redis, pg) {
        this.prisma = prisma;
        this.redis = redis;
        this.pg = pg;
    }

    async createTempSchedule(data, institutionId) {
        const { name, teacherId, courseId, classRoomId, maxStudentCount, weekdays, reservation, attendance, leave } = data;
        const startDate = new Date();
        const { startOfWeek, endOfWeek } = getWeekStartAndEnd(startDate);
        
        const dates = weekdays.map(weekday => ({
            startDate: getWeekDayDatesInRange(startOfWeek, endOfWeek, weekday.day, 'yyyy-MM-dd')[0],
            weekDay: weekday.day,
            startTime: weekday.startTime,
            endTime: weekday.endTime,
        }));

        const scheduleData = dates.map(item => ({
            id: uuidv4(),
            name, 
            teacherId,
            courseId,
            classRoomId,
            maxStudentCount,
            startDate: new Date(item.startDate).getTime(),
            weekDay: item.weekDay,
            startTime: item.startTime,
            endTime: item.endTime,
            currentWeeks: 1,
            totalWeeks: 1,
            institutionId
        }));

        return await this.prisma.ClassesSchedule.createMany({
            data: scheduleData
        });
    }

    async getAllSchedules(query, institutionId) {
        const { startDate, endDate, teacher } = query;
        let sDate = new Date();
        let eDate = new Date();

        if (startDate) {
            sDate = new Date(startDate);
        }
        if (endDate) {
            eDate = new Date(endDate);
        }
        if (!startDate && !endDate) {
            const { startOfWeek, endOfWeek } = getWeekStartAndEnd(new Date());
            sDate = new Date(startOfWeek);
            eDate = new Date(endOfWeek);
        }
        sDate.setHours(0, 0, 0, 0);
        eDate.setHours(23, 59, 59, 999);

        const cacheKey = `schedules:${institutionId}:${sDate.getTime()}-${eDate.getTime()}:${teacher}`;
        let cached = await this.redis.get(cacheKey);

        if (cached) {
            return JSON.parse(cached);
        }

        let teacherCondition = '';
        let queryParams = [institutionId, sDate.getTime(), eDate.getTime()];
        if (teacher) {
            teacherCondition = `AND cs."teacherId" = $4`;
            queryParams.push(teacher);
        }

        const lockKey = `${cacheKey}:lock`;
        let lock = await this.redis.set(lockKey, 1, 'NX', 'EX', 5);
        if (!lock) {
            await new Promise(resolve => setTimeout(resolve, 100));
            cached = await this.redis.get(cacheKey);
            if (cached) {
                return JSON.parse(cached);
            }
        }

        const sqlQuery = `
            SELECT 
                cs.id,
                cs.name,
                cs."startDate",
                cs."weekDay",
                cs."startTime",
                cs."endTime",
                cs."maxStudentCount",
                cs.subject,
                t.name as "teacherName",
                c."type" as "classesType",
                c.name as "className",
                co.name as "courseName",
                count(sws.id) as "studentCount"
            FROM 
                classes_schedules cs
            LEFT JOIN 
                users t ON cs."teacherId" = t.id
            LEFT JOIN 
                classes c ON cs."classesId" = c.id
            LEFT JOIN 
                courses co ON cs."courseId" = co.id
            LEFT JOIN
                student_weekly_schedules sws ON cs.id = sws."classesScheduleId"
            WHERE 
                cs."institutionId" = $1
                AND cs."startDate" >= $2
                AND cs."startDate" <= $3
                ${teacherCondition}
            GROUP BY
                cs.id, t.name, c.name, co.name,c.type
        `;

        const { rows } = await this.pg.query(sqlQuery, queryParams);
        const random = Math.floor(Math.random() * 180) + 120;
        await this.redis.set(cacheKey, JSON.stringify(rows), 'EX', (60 * 60 * 24) + random);
        await this.redis.del(lockKey);

        return rows;
    }

    async getClasses(query, institutionId) {
        const { page = 1, pageSize = 10, name, teacherId } = query;
        const skip = (page - 1) * pageSize;
        const take = pageSize;
      
        const where = {
            institutionId,
            ...(name ? {
                name: {
                    contains: name,
                    mode: 'insensitive'
                }
            } : {}),
            ...(teacherId ? { teacherId } : {}),

        };

      const {startOfWeek,endOfWeek} = getWeekStartAndEnd(new Date());
        const [total, classes] = await Promise.all([
            this.prisma.Classes.count({ where }),
            this.prisma.Classes.findMany({
                where,
                select: {
                    id: true,
                    name: true,
                    startDate: true,
                    endDate: true,
                    status: true,
                    teacher: {
                        select: {
                            id: true,
                            name: true
                        }
                    },
                    course: {
                        select: {
                            id: true,
                            name: true,
                        }
                    },
                    ClassesSchedule: {
                        where: {
                          startDate: {
                            gte: BigInt(startOfWeek.getTime()),
                            lte: BigInt(endOfWeek.getTime())
                          }
                        },
                        select: {
                            currentWeeks: true,
                            totalWeeks: true,
                          }
                    }
                },
                skip,
                take,
                orderBy: {
                    createdAt: 'desc'
                }
            })
        ]);
        console.log(classes, "1111111111111111111111111111111111");

        const transformedResult = classes.map(item => {
            console.log(item, "2222222222222222222222222222222222");
            const currentWeeks = item.ClassesSchedule.length > 0 ? item.ClassesSchedule[0].currentWeeks : null;
            const totalWeeks = item.ClassesSchedule.length > 0 ? item.ClassesSchedule[0].totalWeeks : null;
            delete item.ClassesSchedule;
            return {
                ...item,
                startDate: item.startDate ? Number(item.startDate) : null,
                endDate: item.endDate ? Number(item.endDate) : null,
                currentWeeks,
                totalWeeks,
            };
        });

        return {
            page,
            pageSize,
            total,
            list: transformedResult
        };
    }

    // 创建班级
    async createClass(data, institutionId, userId) {
        const {
            name, maxStudentCount = 20, teacherId, recurrenceType,
            endType, startDate, endDate, courseId, times,
            weekdays, type, daily, reservation, attendance, leave, classRoomId
        } = data;

        const classesId = uuidv4();
        
        // 创建班级
        await this.prisma.classes.create({
            data: {
                id: classesId,
                name,
                teacherId,
                courseId,
                maxStudentCount,
                startDate,
                endType,
                times,
                endDate,
                type,
                recurrenceType,
                ...(classRoomId && { classRoomId }),
                ...(reservation.enabled ? {
                    isReserve: true,
                    appointmentStartTime: reservation.appointmentStartTime,
                    appointmentEndTime: reservation.appointmentEndTime
                } : {}),
                ...(attendance.enabled ? {
                    isQRCodeAttendance: attendance.studentScan,
                    isAutoCheckIn: attendance.autoSystem
                } : {}),
                ...(leave.enabled ? {
                    isOnLeave: true,
                    leaveDeadline: leave.leaveDeadline
                } : {}),
                institutionId
            }
        });

        // 创建班级时间详细表
        const classesTimePromises = weekdays.map(item => {
            const id = uuidv4();
            // 获取该星期几的所有日期
            const dateList = endType === 'number_of_times' ?
                getTuesdaysInWeeks(startDate, times, item.day) :
                getTuesdaysInWeeks(startDate, times, item.day);
            
            // 获取最后一个日期作为结束日期
            const itemEndDate = dateList.length > 0 ? 
                new Date(dateList[dateList.length - 1]).getTime().toString() : 
                endDate.toString();
            
            return this.prisma.classesTime.create({
                data: {
                    id,
                    classesId,
                    weekDay: item.day,
                    startTime: item.startTime,
                    endTime: item.endTime,
                    recurrenceType,
                    endType,
                    startDate: startDate.toString(),
                    endDate: itemEndDate,
                    institutionId
                }
            });
        });
        await Promise.all(classesTimePromises);

        // 创建班级计划
        const classesScheduleData = {
            classesId,
            courseId,
            teacherId,
            maxStudentCount,
            subject: name,
            ...(reservation.enabled ? {
                isReserve: true,
                appointmentStartTime: reservation.appointmentStartTime,
                appointmentEndTime: reservation.appointmentEndTime
            } : {}),
            ...(attendance.enabled ? {
                isQRCodeAttendance: attendance.studentScan,
                isAutoCheckIn: attendance.autoSystem
            } : {}),
            ...(leave.enabled ? {
                isOnLeave: true,
                leaveDeadline: leave.leaveDeadline
            } : {}),
            institutionId
        };

        const schedules = [];
        if (recurrenceType === 'weekly') {
            if (!weekdays || !Array.isArray(weekdays) || weekdays.length === 0) {
                throw new Error('请选择周几');
            }

            const dayData = weekdays.map(item => ({
                day: item.day,
                list: endType === 'number_of_times' ?
                    getTuesdaysInWeeks(startDate, times, item.day) :
                    getTuesdaysInWeeks(startDate, times, item.day),
                startTime: item.startTime,
                endTime: item.endTime
            }));

            const newDayData = [];
            for (const item of dayData) {
                for (const date of item.list) {
                    newDayData.push({
                        day: item.day,
                        startTime: item.startTime,
                        endTime: item.endTime,
                        startDate: date
                    });
                }
            }

            newDayData.sort((a, b) => new Date(a.startDate) - new Date(b.startDate));

            const totalWeeks = newDayData.length;
            await this.prisma.classes.update({
                where: { id: classesId },
                data: {
                    times: totalWeeks,
                    endDate: new Date(newDayData[newDayData.length - 1].startDate).getTime()
                }
            });

            schedules.push(...newDayData.map((item, index) => ({
                id: uuidv4(),
                startDate: new Date(item.startDate).getTime(),
                weekDay: item.day,
                startTime: item.startTime,
                endTime: item.endTime,
                currentWeeks: index + 1,
                totalWeeks,
                ...classesScheduleData
            })));
        } else if (recurrenceType === 'daily') {
            const dates = endType === 'times' ? 
                getDaysInRange(startDate, endDate) : 
                getDaysInRange(startDate, endDate, times - 1);
            
            const totalWeeks = dates.length;
            await this.prisma.classes.update({
                where: { id: classesId },
                data: {
                    times: totalWeeks,
                    endDate: new Date(dates[dates.length - 1]).getTime()
                }
            });

            schedules.push(...dates.map((date, index) => ({
                id: uuidv4(),
                startDate: new Date(date).getTime(),
                weekDay: new Date(date).getDay(),
                startTime: daily.startTime || '00:00',
                endTime: daily.endTime || '23:59',
                currentWeeks: index + 1,
                totalWeeks,
                ...classesScheduleData
            })));
        }

        await this.prisma.classesSchedule.createMany({
            data: schedules
        });

        return classesId;
    }

    async checkTeacherFree(data, institutionId) {
        const { times, recurrenceType, daily, startDate, endDate, endType, weekdays } = data;
        
        // 获取全部老师
        const teachersResult = await this.prisma.userInstitution.findMany({
            where: { institutionId },
            select: {
                user: {
                    select: {
                        id: true,
                        name: true,
                    }
                }
            }
        });
        
        const teachers = teachersResult.map(item => item.user);
        const teacherIds = teachers.map(item => item.id);
        const teacherFreeTime = [];

        if (recurrenceType === 'weekly') {
            const dayData = weekdays.map(item => 
                endType === 'number_of_times' ?
                    getTuesdaysInWeeks(startDate, times, item.day) :
                    getTuesdaysInWeeks(startDate, times, item.day)
            ).flat();

            dayData.sort((a, b) => a - b);

            for (const dataItem of dayData) {
                for (const wkItem of weekdays) {
                    const startTime = formatTimeString(wkItem.startTime);
                    const endTime = formatTimeString(wkItem.endTime);
                    const teacherFreeTimeItem = await this.prisma.classesSchedule.findMany({
                        where: {
                            teacherId: { in: teacherIds },
                            startDate: new Date(dataItem).getTime(),
                            weekDay: wkItem.day,
                            AND: [
                                { startTime: { lte: endTime } },
                                { endTime: { gte: startTime } }
                            ]
                        },
                        select: {
                            teacher: {
                                select: {
                                    id: true,
                                    name: true
                                }
                            }
                        }
                    });
                    teacherFreeTime.push(...teacherFreeTimeItem);
                }
            }
        } else if (recurrenceType === 'daily') {
            const dates = endType === 'times' ? 
                getDaysInRange(startDate, endDate) : 
                getDaysInRange(startDate, endDate, times - 1);

            for (const date of dates) {
                const startTime = formatTimeString(daily.startTime);
                const endTime = formatTimeString(daily.endTime);
                const teacherFreeTimeItem = await this.prisma.classesSchedule.findMany({
                    where: {
                        teacherId: { in: teacherIds },
                        startDate: new Date(date).getTime(),
                        AND: [
                            { startTime: { lte: endTime } },
                            { endTime: { gte: startTime } }
                        ]
                    },
                    select: {
                        teacher: {
                            select: {
                                id: true,
                                name: true
                            }
                        }
                    }
                });
                teacherFreeTime.push(...teacherFreeTimeItem);
            }
        }

        const uniqueTeacherFreeTime = teacherFreeTime.filter((item, index, self) =>
            index === self.findIndex((t) => t.teacher.id === item.teacher.id)
        );

        teachers.forEach(item => {
            item.freeTime = !uniqueTeacherFreeTime.some(t => t.teacher.id === item.id);
        });

        return teachers;
    }

    async getClassesSelect(query, institutionId) {
        const { page = 1, pageSize = 10, name } = query;
        const skip = (page - 1) * pageSize;
        const take = pageSize;

        const [result, total] = await Promise.all([
            this.prisma.classes.findMany({
                where: {
                    institutionId,
                    ...(name ? { name: { contains: name, mode: 'insensitive' } } : {})
                },
                select: {
                    id: true,
                    name: true,
                    startDate: true,
                    teacher: {
                        select: {
                            id: true,
                            name: true
                        }
                    }
                },
                skip,
                take
            }),
            this.prisma.classes.count({
                where: { 
                    institutionId,
                    ...(name ? { name: { contains: name, mode: 'insensitive' } } : {})
                }
            })
        ]);

        return {
            list: result.map(item => ({
                ...item,
                startDate: item.startDate ? Number(item.startDate) : null
            })),
            total,
            page,
            pageSize
        };
    }

    async addClassCourse(classesId, courseId, institutionId) {
        const id = TwitterSnowflake.generate().toString();
        
        await this.prisma.courseClasses.create({
            data: {
                id,
                classesId,
                courseId,
                institutionId
            }
        });

        // 更新班级课程信息
        await this.prisma.classes.update({
            where: { id: classesId },
            data: { courseId }
        });
    }

    async updateClassCourse(classesId, courseId, institutionId) {
        const currentTime = new Date().getTime();
        
        const courseClasses = await this.prisma.courseClasses.findFirst({
            where: {
                classesId,
                institutionId
            }
        });

        if (!courseClasses) {
            throw new Error('班级课程不存在');
        }

        await Promise.all([
            // 更新课程关联
            this.prisma.courseClasses.update({
                where: { id: courseClasses.id },
                data: { courseId }
            }),
            // 更新未来课节的课程
            this.prisma.classesSchedule.updateMany({
                where: {
                    classesId,
                    startDate: { gt: currentTime },
                    institutionId
                },
                data: { courseId }
            })
        ]);
    }

    async addStudents(classesId, studentIds, institutionId, operatorId) {
        const currentTime = new Date().getTime();

        // 检查学生是否已经在班级中
        const existingStudents = await this.prisma.studentClasses.findMany({
            where: {
                classesId,
                studentId: { in: studentIds },
                institutionId
            },
            select: {
                studentId: true
            }
        });

        const existingStudentIds = existingStudents.map(s => s.studentId);
        const newStudentIds = studentIds.filter(id => !existingStudentIds.includes(id));

        if (newStudentIds.length === 0) {
            throw new Error('所选学生已在班级中');
        }

        // 添加新学生到班级
        const studentClassesData = newStudentIds.map(studentId => ({
            id: uuidv4(),
            classesId,
            studentId,
            institutionId,
            operatorId,
            joinDate: currentTime,
            operatorTime: currentTime
        }));

        try {
            await this.prisma.studentClasses.createMany({
                data: studentClassesData
            });
        } catch (error) {
            if (error.code === 'P2002') {
                throw new Error('添加学生失败：存在重复记录');
            }
            throw error;
        }

        // 获取未来的课节
        const futureSchedules = await this.prisma.classesSchedule.findMany({
            where: {
                classesId,
                startDate: { gt: currentTime },
                institutionId
            }
        });

        // 为每个学生创建课节记录
        const scheduleStudentData = [];
        futureSchedules.forEach(schedule => {
            newStudentIds.forEach(studentId => {
                scheduleStudentData.push({
                    id: uuidv4(),
                    classesScheduleId: schedule.id,
                    studentId,
                    studentType: 'fixed',
                    institutionId
                });
            });
        });

        if (scheduleStudentData.length > 0) {
            try {
                await this.prisma.studentWeeklySchedule.createMany({
                    data: scheduleStudentData
                });
            } catch (error) {
                // 如果创建课节记录失败，回滚学生添加
                await this.prisma.studentClasses.deleteMany({
                    where: {
                        classesId,
                        studentId: { in: newStudentIds },
                        institutionId
                    }
                });
                throw new Error('创建课节记录失败：' + error.message);
            }
        }
    }

    async removeStudent(classesId, studentId, institutionId) {
        const currentTime = new Date().getTime();

        // 删除学生班级关联
        await this.prisma.studentClasses.deleteMany({
            where: {
                classesId,
                studentId,
                institutionId
            }
        });

        // 删除未来课节的学生记录
        await this.prisma.studentWeeklySchedule.deleteMany({
            where: {
                studentId,
                institutionId,
                classesSchedule: {
                    classesId,
                    startDate: { gt: currentTime }
                }
            }
        });
    }

    async updateClass(classesId, data, institutionId) {
        const {
            name, teacherId, status, courseId, isReserve,
            appointmentStartTime, appointmentEndTime, isQRCodeAttendance,
            isAutoCheckIn, isOnLeave, leaveDeadline, isShowWeekCount,
            classRoomId, maxStudentCount
        } = data;

        const updateData = {
            ...(maxStudentCount && { maxStudentCount: Number(maxStudentCount) }),
            ...(classRoomId && { classRoomId }),
            ...(isReserve && {
                isReserve: true,
                appointmentStartTime: Number(appointmentStartTime),
                appointmentEndTime: Number(appointmentEndTime)
            }),
            ...(isQRCodeAttendance && { isQRCodeAttendance: true }),
            ...(isAutoCheckIn && { isAutoCheckIn: true }),
            ...(isOnLeave && {
                isOnLeave: true,
                leaveDeadline: Number(leaveDeadline)
            }),
            ...(isShowWeekCount && { isShowWeekCount: true }),
            ...(name && { name }),
            ...(teacherId && { teacherId }),
            ...(status && { status })
        };

        await this.prisma.classes.update({
            where: { id: classesId, institutionId },
            data: updateData
        });

        if (courseId) {
            await this.updateClassCourse(classesId, courseId, institutionId);
        }
    }

    async getClassDetail(classesId, institutionId) {
        const result = await this.prisma.classes.findFirst({
            where: {
                id: classesId,
                institutionId
            },
            select: {
                id: true,
                name: true,
                endDate: true,
                startDate: true,
                remarks: true,
                times: true,
                status: true,
                type: true,
                isReserve: true,
                maxStudentCount: true,
                appointmentStartTime: true,
                appointmentEndTime: true,
                isQRCodeAttendance: true,
                isAutoCheckIn: true,
                isOnLeave: true,
                isShowWeekCount: true,
                leaveDeadline: true,
                timeDetails: {
                    select: {
                        id: true,
                        weekDay: true,
                        startTime: true,
                        endTime: true
                    }
                },
                students: {
                    select: {
                        id: true,
                        student: {
                            select: {
                                id: true,
                                name: true,
                                phone: true,
                                type: true
                            }
                        },
                        operatorTime: true,
                        operator: {
                            select: {
                                name: true
                            }
                        },
                        type: true,
                        joinDate: true
                    }
                },
                course: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                teacher: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        if (!result) {
            throw new Error('班级不存在');
        }

        const schedules = await this.prisma.classesSchedule.findMany({
            where: { classesId },
            select: {
                id: true,
                startDate: true,
                weekDay: true,
                startTime: true,
                endTime: true,
                currentWeeks: true,
                totalWeeks: true,
                subject: true,
                courses: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                teacher: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                StudentWeeklySchedule: {
                    select: {
                        id: true,
                        status: true,
                        student: {
                            select: {
                                id: true,
                                name: true
                            }
                        }
                    }
                }
            },
            orderBy: {
                startDate: 'asc'
            }
        });

        return {
            ...result,
            startDate: result.startDate ? Number(result.startDate) : null,
            endDate: result.endDate ? Number(result.endDate) : null,
            times: result.times ? Number(result.times) : null,
            students: result.students.map(item => ({
                ...item,
                joinDate: item.joinDate ? Number(item.joinDate) : null,
                operatorTime: item.operatorTime ? Number(item.operatorTime) : null
            })),
            classesSchedule: schedules.map(item => ({
                ...item,
                startDate: item.startDate ? Number(item.startDate) : null
            }))
        };
    }

    async getClassSchedules(classesId, institutionId) {
        const schedules = await this.prisma.classesSchedule.findMany({
            where: {
                classesId,
                institutionId
            },
            select: {
                id: true,
                startDate: true,
                startTime: true,
                endTime: true,
                currentWeeks: true,
                courses: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                teacher: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                StudentWeeklySchedule: {
                    select: {
                        id: true,
                        status: true,
                        studentType: true,
                        operator: {
                            select: {
                                name: true
                            }
                        },
                        operatorTime: true,
                        student: {
                            select: {
                                name: true,
                                phone: true
                            }
                        }
                    }
                }
            }
        });

        return schedules.map(item => ({
            ...item,
            startDate: item.startDate ? Number(item.startDate) : null,
            StudentWeeklySchedule: item.StudentWeeklySchedule.map(student => ({
                ...student,
                operatorTime: student.operatorTime ? Number(student.operatorTime) : null
            }))
        }));
    }

    async getScheduleDetail(scheduleId, institutionId) {
        const schedule = await this.prisma.classesSchedule.findFirst({
            where: {
                id: scheduleId,
                institutionId
            },
            select: {
                id: true,
                name: true,
                startDate: true,
                weekDay: true,
                startTime: true,
                endTime: true,
                currentWeeks: true,
                totalWeeks: true,
                maxStudentCount: true,
                isReserve: true,
                appointmentStartTime: true,
                appointmentEndTime: true,
                isQRCodeAttendance: true,
                isAutoCheckIn: true,
                isOnLeave: true,
                leaveDeadline: true,
                isShowWeekCount: true,
                subject: true,
                StudentWeeklySchedule: {
                    select: {
                        id: true,
                        student: {
                            select: {
                                id: true,
                                name: true,
                                phone: true,
                                gender: true
                            }
                        },
                        status: true,
                        studentType: true,
                        operator: {
                            select: {
                                name: true
                            }
                        },
                        operatorTime: true
                    }
                },
                courses: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                        deductionPerClass: true
                    }
                },
                teacher: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                classes: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        if (!schedule) {
            throw new Error('课节不存在');
        }

        return {
            ...schedule,
            startDate: schedule.startDate ? Number(schedule.startDate) : null,
            StudentWeeklySchedule: schedule.StudentWeeklySchedule.map(item => ({
                ...item,
                operatorTime: item.operatorTime ? Number(item.operatorTime) : null
            }))
        };
    }

    async updateSchedule(scheduleId, data, institutionId) {
        const {
            name, startTime, endTime, subject, courseId, teacherId,
            date, isReserve, appointmentStartTime, appointmentEndTime,
            isQRCodeAttendance, isAutoCheckIn, isOnLeave, leaveDeadline,
            isShowWeekCount, classRoomId, maxStudentCount
        } = data;

        await this.prisma.classesSchedule.update({
            where: {
                id: scheduleId,
                institutionId
            },
            data: {
                ...(maxStudentCount && { maxStudentCount: Number(maxStudentCount) }),
                ...(name && { name }),
                ...(startTime && { startTime }),
                ...(endTime && { endTime }),
                ...(subject && { subject }),
                ...(courseId && { courseId }),
                ...(teacherId && { teacherId }),
                ...(date && { startDate: date }),
                ...(isReserve && {
                    isReserve: true,
                    appointmentStartTime: Number(appointmentStartTime),
                    appointmentEndTime: Number(appointmentEndTime)
                }),
                ...(isQRCodeAttendance && { isQRCodeAttendance: true }),
                ...(isAutoCheckIn && { isAutoCheckIn: true }),
                ...(isOnLeave && {
                    isOnLeave: true,
                    leaveDeadline: Number(leaveDeadline),
                    isShowWeekCount
                }),
                ...(classRoomId && { classRoomId })
            }
        });
    }

    async addScheduleStudent(scheduleId, studentData, institutionId, operatorId) {
        const { students, type, studentIds } = studentData;

        const schedule = await this.prisma.classesSchedule.findFirst({
            where: {
                id: scheduleId,
                institutionId
            },
            select: {
                teacherId: true
            }
        });


        if(studentIds.length > 0) {
            await this.prisma.studentWeeklySchedule.createMany({
                data: studentIds.map(studentId => ({
                    classesScheduleId: scheduleId,
                studentId,
                    studentType: type,
                    operatorId,
                    operatorTime: new Date().getTime(),
                    institutionId
                }))
            });
        } else {
            await this.prisma.studentWeeklySchedule.create({
                data: {
                    classesScheduleId: scheduleId,
                    studentId: students.studentId,
                    studentType: students.type,
                    operatorId,
                    operatorTime: new Date().getTime(),
                    institutionId
                }
            });
        }

        if (type === 'trial' && schedule.teacherId) {
            let student;
            if(studentIds.length > 0) {
                student = await this.prisma.student.findMany({
                    where: {
                        id: { in: studentIds }
                    },
                    select: {
                        name: true
                    }
                }); 
            }else {
                student = await this.prisma.student.findFirst({
                    where: {
                        id: students.studentId
                    },
                    select: {
                        name: true
                    }
                });
            }
            student = student.map(item => item.name);
            for(let i = 0; i < student.length; i++) {
                const payload = {
                    type: 'TRIAL_STUDENT_ADDED',
                    title: '试听学员添加',
                    student: {
                        name: student[i]
                    }
                };
                await sendToSpecifiedUser(this.fastify, schedule.teacherId, institutionId, payload);
            }
        }
    }

    async removeScheduleStudents(scheduleId, studentIds, institutionId) {
        await this.prisma.studentWeeklySchedule.deleteMany({
            where: {
                classesScheduleId: scheduleId,
                studentId: { in: studentIds },
                institutionId
            }
        });
    }

    async updateStudentAttendance(scheduleId, studentId, status, institutionId, operatorId) {
        const result = await this.prisma.studentWeeklySchedule.findFirst({
            where: {
                classesScheduleId: scheduleId,
                studentId,
                institutionId
            },
            select: {
                id: true,
                studentProductId: true,
                attendanceCount: true,
                studentType: true,
                status: true,
                attendanceAmount: true,
                classesSchedule: {
                    select: {
                        courses: {
                            select: {
                                deductionPerClass: true,
                                isDeductOnAttendance: true,
                                isDeductOnLeave: true,
                                isDeductOnAbsence: true,
                                ProductCourse: {
                                    select: {
                                        id: true,
                                        productId: true
                                    }
                                }
                            }
                        }
                    }
                },
                student: {
                    select: {
                        StudentProduct: {
                            where: {
                                enrollmentStatus: 'active'
                            },
                            select: {
                                id: true,
                                productId: true,
                                remainingSessionCount: true,
                                remainingBalance: true,
                                sessionUnitPrice: true,
                                paymentStatus: true,
                                startDate: true,
                                endDate: true,
                                product: {
                                    select: {
                                        id: true,
                                        timeLimitedUsage: true,
                                        timeLimitType: true,
                                        validTimeRange: true
                                    }
                                }
                            },
                            orderBy: {
                                createdAt: 'desc'
                            }
                        }
                    }
                }
            }
        });

        if (!result) {
            throw new Error('学员不存在');
        }

        if (result.studentType === 'trial') {
            throw new Error('试听学员不能考勤');
        }

        // 如果状态相同，退还课时
        if (result.status === status) {
            if (result.studentProductId) {
                const productResult = await this.prisma.studentProduct.findFirst({
                    where: { id: result.studentProductId }
                });

                const newRemainingCount = Number(productResult.remainingSessionCount) + Number(result.attendanceCount);
                const newRemainingBalance = Number(productResult.remainingBalance) + Number(result.attendanceAmount);

                await this.prisma.studentProduct.update({
                    where: { id: productResult.id },
                    data: {
                        remainingSessionCount: Number(newRemainingCount).toFixed(2),
                        remainingBalance: Number(newRemainingBalance).toFixed(2),
                        enrollmentStatus: newRemainingCount > 0 ? 'active' : 'completed'
                    }
                });
            }

            await this.prisma.studentWeeklySchedule.update({
                where: { id: result.id },
                data: {
                    status: 'unattended',
                    attendanceCount: 0,
                    studentProductId: null,
                    operatorId: null,
                    operatorTime: null,
                    attendanceAmount: 0,
                    productId: null
                }
            });

            return;
        }

        // 处理考勤扣课时
        const { courses } = result.classesSchedule;
        const { deductionPerClass, isDeductOnAttendance, isDeductOnLeave, isDeductOnAbsence } = courses;

        let product = null;
        let productIndex = 0;
        let isFound = false;
        let productCourse = null;

        if (result.student.StudentProduct.length < 1) {
            throw new Error('学员没有产品');
        }

        while (productIndex < result.student.StudentProduct.length) {
            product = result.student.StudentProduct[productIndex];
            productCourse = courses.ProductCourse.filter(item => item.productId === product.productId);
            
            if (Number(product.remainingSessionCount) >= Number(deductionPerClass) && productCourse.length > 0) {
                isFound = true;
                break;
            }
            productIndex++;
        }

        if (productCourse.length === 0) {
            throw new Error('没有课程对应的套餐');
        }

        if (!isFound) {
            throw new Error('当前产品课时不足');
        }

        // 处理限时限次消费
        if (product.product.validTimeRange === 'consumption-date' && !product.startDate) {
            const startDate = new Date();
            const endDate = new Date();
            const timeLimitedUsage = Number(product.product.timeLimitedUsage);
            
            if (product.product.timeLimitType === 'daily') {
                endDate.setDate(endDate.getDate() + timeLimitedUsage);
            } else {
                endDate.setMonth(endDate.getMonth() + timeLimitedUsage);
            }

            await this.prisma.studentProduct.update({
                where: { id: product.id },
                data: {
                    startDate: startDate.getTime(),
                    endDate: endDate.getTime()
                }
            });
        }

        // 计算扣除课时和金额
        let newRemainingCount = Number(product.remainingSessionCount);
        const pricePerClass = Number(product.sessionUnitPrice) * Number(deductionPerClass);
        let remainingBalance = Number(product.remainingBalance);

        if (
            (status === 'attendance' && isDeductOnAttendance) ||
            (status === 'leave' && isDeductOnLeave) ||
            (status === 'absent' && isDeductOnAbsence)
        ) {
            newRemainingCount -= Number(deductionPerClass);
            remainingBalance -= pricePerClass;
        }

        // 更新学员产品
        const newUnitPrice = remainingBalance / newRemainingCount;
        await this.prisma.studentProduct.update({
            where: { id: product.id },
            data: {
                remainingSessionCount: Number(newRemainingCount).toFixed(3),
                remainingBalance: Number(remainingBalance).toFixed(3),
                sessionUnitPrice: Number(newUnitPrice).toFixed(3),
                enrollmentStatus: newRemainingCount > 0 ? 'active' : 'completed'
            }
        });

        // 更新考勤记录
        await this.prisma.studentWeeklySchedule.update({
            where: { id: result.id },
            data: {
                status,
                attendanceCount: Number(deductionPerClass),
                studentProductId: product.id,
                operatorId,
                operatorTime: new Date().getTime(),
                attendanceAmount: Number(pricePerClass).toFixed(3),
                productId: product.productId
            }
        });
    }

    async getClassStudents(classesId, institutionId) {
        const students = await this.prisma.studentClasses.findMany({
            where: {
                classesId,
                institutionId
            },
            select: {
                joinDate: true,
                type: true,
                student: {
                    select: {
                        phone: true,
                        name: true,
                        type: true
                    }
                },
                operatorTime: true,
                operator: {
                    select: {
                        name: true
                    }
                }
            }
        });

        return students.map(item => ({
            ...item,
            operatorTime: item.operatorTime ? Number(item.operatorTime) : null,
            joinDate: item.joinDate ? Number(item.joinDate) : null
        }));
    }

    async getScheduleAttendance(classesId, scheduleId, institutionId) {
        const attendance = await this.prisma.studentWeeklySchedule.findMany({
            where: {
                classesScheduleId: scheduleId,
                institutionId
            },
            select: {
                id: true,
                status: true,
                operatorTime: true,
                studentType: true,
                student: {
                    select: {
                        id: true,
                        name: true,
                        phone: true
                    }
                },
                operator: {
                    select: {
                        name: true
                    }
                }
            }
        });

        return attendance.map(item => ({
            ...item,
            operatorTime: item.operatorTime ? Number(item.operatorTime) : null
        }));
    }

    async getClassAttendance(classesId, institutionId) {
        const schedules = await this.prisma.classesSchedule.findMany({
            where: {
                classesId,
                institutionId
            },
            select: {
                id: true,
                startDate: true,
                weekDay: true,
                startTime: true,
                endTime: true,
                currentWeeks: true,
                StudentWeeklySchedule: {
                    select: {
                        status: true,
                        student: {
                            select: {
                                id: true,
                                name: true
                            }
                        }
                    }
                }
            }
        });

        return schedules.map(item => ({
            ...item,
            startDate: item.startDate ? Number(item.startDate) : null
        }));
    }
} 
