import { PrismaClient } from '@prisma/client';
import { Redis } from 'ioredis';
import pg from 'pg';
import { ClassService } from './services/classService.js';

const { Pool } = pg;

async function testGetClassesFix() {
    console.log('Testing getClasses fix for currentWeeks and totalWeeks...');
    
    // Initialize clients
    const prisma = new PrismaClient();
    const redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD || '',
        db: 1,
        keyPrefix: "sixue:"
    });
    const pgPool = new Pool({
        connectionString: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/cardmees'
    });

    try {
        // Redis connects automatically
        console.log('✓ Redis client initialized');
        
        // Connect to Prisma
        await prisma.$connect();
        console.log('✓ Connected to Prisma');
        
        // Create ClassService instance
        const classService = new ClassService(prisma, redis, pgPool);
        
        // Test the getClasses method
        console.log('\nTesting getClasses method...');
        
        // First, let's see if there are any classes in the database
        const allClasses = await prisma.classes.findMany({
            take: 5,
            select: {
                id: true,
                name: true,
                institutionId: true
            }
        });
        
        if (allClasses.length === 0) {
            console.log('⚠️  No classes found in database. Creating a test class...');
            
            // Create a test class with schedules
            const testInstitutionId = 'test-institution-id';
            const testClassId = 'test-class-id';
            
            // Create test class
            await prisma.classes.create({
                data: {
                    id: testClassId,
                    name: 'Test Class',
                    institutionId: testInstitutionId,
                    startDate: BigInt(Date.now()),
                    endDate: BigInt(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
                    status: 'active'
                }
            });
            
            // Create test schedules
            await prisma.classesSchedule.createMany({
                data: [
                    {
                        id: 'schedule-1',
                        classesId: testClassId,
                        startDate: BigInt(Date.now()),
                        weekDay: 1,
                        startTime: '09:00',
                        endTime: '10:00',
                        currentWeeks: 1,
                        totalWeeks: 10,
                        institutionId: testInstitutionId
                    },
                    {
                        id: 'schedule-2',
                        classesId: testClassId,
                        startDate: BigInt(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week later
                        weekDay: 1,
                        startTime: '09:00',
                        endTime: '10:00',
                        currentWeeks: 2,
                        totalWeeks: 10,
                        institutionId: testInstitutionId
                    }
                ]
            });
            
            console.log('✓ Created test class and schedules');
            
            // Test with the created class
            const result = await classService.getClasses({}, testInstitutionId);
            
            console.log('\nTest Results:');
            console.log('Total classes found:', result.total);
            console.log('Classes with currentWeeks/totalWeeks:');
            
            result.list.forEach((cls, index) => {
                console.log(`  ${index + 1}. ${cls.name}:`);
                console.log(`     currentWeeks: ${cls.currentWeeks}`);
                console.log(`     totalWeeks: ${cls.totalWeeks}`);
                console.log(`     Status: ${cls.currentWeeks !== null && cls.totalWeeks !== null ? '✓ SUCCESS' : '✗ FAILED'}`);
            });
            
            // Clean up test data
            await prisma.classesSchedule.deleteMany({
                where: { classesId: testClassId }
            });
            await prisma.classes.delete({
                where: { id: testClassId }
            });
            console.log('✓ Cleaned up test data');
            
        } else {
            console.log(`Found ${allClasses.length} classes in database. Testing with existing data...`);
            
            // Test with existing classes
            const testInstitutionId = allClasses[0].institutionId;
            const result = await classService.getClasses({}, testInstitutionId);
            
            console.log('\nTest Results:');
            console.log('Total classes found:', result.total);
            console.log('Classes with currentWeeks/totalWeeks:');
            
            let successCount = 0;
            result.list.forEach((cls, index) => {
                const hasData = cls.currentWeeks !== null && cls.totalWeeks !== null;
                if (hasData) successCount++;
                
                console.log(`  ${index + 1}. ${cls.name}:`);
                console.log(`     currentWeeks: ${cls.currentWeeks}`);
                console.log(`     totalWeeks: ${cls.totalWeeks}`);
                console.log(`     Status: ${hasData ? '✓ SUCCESS' : '✗ NO DATA'}`);
            });
            
            console.log(`\nSummary: ${successCount}/${result.list.length} classes have currentWeeks/totalWeeks data`);
        }
        
        console.log('\n🎉 Test completed successfully!');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        // Cleanup
        await redis.quit();
        await prisma.$disconnect();
        await pgPool.end();
        console.log('✓ Disconnected from all services');
    }
}

// Run the test
testGetClassesFix().catch(console.error);
